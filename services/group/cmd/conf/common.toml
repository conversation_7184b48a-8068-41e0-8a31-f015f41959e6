# 前端机公共配置文件
#
# 注意编写规范

############# 服务基础配置 ##################

[service]
mode = 'debug'
ccs_name = 'alpha'
# is_saas 设置是否是 saas，私有化部署，不加这个配置项
is_saas = true

############# 各种证书配置 ##################
[certs]
rsa_2048 = 'certs/rsa2048.prvkey'
rsa_4096 = 'certs/rsa4096.prvkey'
crt = "certs/client.crt"
key = "certs/client.key"
ca = "certs/ca.crt"
qax_grpc = 'certs/qianxin.crt'

############# Mongodb 配置 ##################

[mongodb_group]
# 资源前缀，库名前缀，比如 alpha-portal，那么配 alpha-
resource_prefix = 'alpha-'
addr = 'mongodb.outside.rongma.tech:27017'
username = 'alpha-rmclient'
password = 'zo3RdOgGio*F'

############# Redis 配置 ##################

[redis_default]
# connection_mode 连接方式，0 为单机模式，1 为集群模式
connection_mode = 0
addr = ['192.168.111.185:36379']
password = ''
db = 0

[grpc_info]
is_cheack_cert = false
addr = '192.168.111.195:18602'

