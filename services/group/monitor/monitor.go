package monitor

import (
	"context"
	"sync"
	"sync/atomic"
	"time"

	infoGrpc "rm.git/cloud_api/rm_common_protos.git/proto_go/info_v1"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"
	"rongma.com/group/services/group/domain/dto"
	"rongma.com/group/services/group/domain/entity"
	"rongma.com/group/services/group/infrastructure/client"
	"rongma.com/group/services/group/infrastructure/config"
	"rongma.com/group/services/group/infrastructure/repository"
)

type Monitor struct {
	lock          *RedisLock
	checkInterval time.Duration
}

func Init(ctx context.Context, conf *config.AppConfig) *Monitor {
	checkInterval := 3 * time.Minute
	if conf.Monitor.CheckIntervalMinutes > 0 {
		checkInterval = time.Duration(conf.Monitor.CheckIntervalMinutes) * time.Minute
	}

	log.Infof("数据变化监控检查时间间隔: %v", checkInterval)

	lock := NewRedisLock(client.Redis, "policy_lock", 60*time.Second)
	//尝试成为主节点，如果失败则成为从节点
	if err := lock.StartMaster(ctx); err != nil {
		if err := lock.StartSlave(ctx); err != nil {
			log.Fatalf("获取锁失败: %v", err)
		}
	}
	return &Monitor{
		checkInterval: checkInterval,
		lock:          lock,
	}
}
func (m *Monitor) Start() {
	m.MainWorkJob()

	//定期检查节点状态
	go func() {
		ticker := time.NewTicker(m.checkInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				if m.lock.IsMaster() {
					log.Infof("当前节点为master")
					m.MainWorkJob()
				}
			}
		}
	}()

}

func (m *Monitor) Stop() {
	m.lock.Stop()
}

func (m *Monitor) MainWorkJob() {
	m.DealGroupInfos()
	m.DealGroupRelationInfos()
}

// 获取分组表中sync_status为0的数据
func (m *Monitor) DealGroupInfos() {
	log.Infof("[group] 开始处理分组数据同步")
	var limit int64 = 20
	for {
		groupInfos, err := repository.Group.GetUnSyncGroupInfos(context.Background(), limit)
		if err != nil {
			log.Errorf("获取分组数据失败: %v", err)
			return
		}
		if len(groupInfos) == 0 {
			log.Infof("没有需要同步的分组数据")
			return
		}
		// 同步数据到远端服务
		_, err = m.syncGroupInfosToRemote(groupInfos)
		if err != nil {
			log.Errorf("同步分组数据到远端失败: %v", err)
			return
		}

		// 更新本地数据的同步状态
		var orgNameGidsMap map[string][]int64 = make(map[string][]int64)
		for _, groupInfo := range groupInfos {
			orgNameGidsMap[groupInfo.OrgName] = append(orgNameGidsMap[groupInfo.OrgName], groupInfo.Gid)
		}
		for orgName, gids := range orgNameGidsMap {
			_, err := repository.Group.UpdateGroupSyncStatus(context.Background(), &dto.UpdateGroupSyncStatusRequest{
				Gids:    gids,
				OrgName: orgName,
			}, 1)
			if err != nil {
				log.Errorf("更新分组同步状态失败: %v", err)
				return
			}
		}
	}
	log.Infof("[group] 处理分组数据同步完成")
}

func (m *Monitor) syncGroupInfosToRemote(groupInfos []*entity.Group) (interface{}, error) {
	infos := make([]*infoGrpc.DeviceGroupInfo, 0)
	for _, groupInfo := range groupInfos {
		info := &infoGrpc.DeviceGroupInfo{
			Gid:           groupInfo.Gid,
			OrgName:       groupInfo.OrgName,
			GroupType:     groupInfo.GroupType,
			GroupName:     groupInfo.GroupName,
			ParentGid:     groupInfo.ParentGid,
			Description:   groupInfo.Description,
			IsWorkplace:   groupInfo.IsWorkplace,
			WorkplaceJson: groupInfo.WorkplaceJson,
			CreateBy:      groupInfo.CreateBy,
			UpdateBy:      groupInfo.UpdateBy,
			UpdateTime:    groupInfo.UpdateTime,
			CreateTime:    groupInfo.CreateTime,
			HasSubGroup:   groupInfo.HasSubGroup,
			Status:        groupInfo.Status,
		}
		infos = append(infos, info)
	}
	if len(infos) == 0 {
		return nil, nil
	}
	res, err := client.InfoGrpcClient.ReportDeviceGroupInfos(context.Background(), &infoGrpc.DeviceGroupInfosRequest{Infos: infos})
	if err != nil {
		log.Errorf("同步分组数据到远端失败: %v", err)
		return nil, err
	}
	if res.Count == int32(len(infos)) {
		log.Infof("同步分组数据到远端成功: %v", res.Count)
	} else {
		log.Errorf("同步分组数据到远端失败, total: %d, succeed: %d", len(infos), res.Count)
	}
	return nil, nil
}

func (m *Monitor) DealGroupRelationInfos() {
	log.Infof("[group relation] 开始处理分组关系数据同步")
	var limit int64 = 20
	for {
		groupRelationInfos, err := repository.Group.GetUnSyncGroupRelationInfos(context.Background(), limit)
		if err != nil {
			log.Errorf("获取分组关系数据失败: %v", err)
			return
		}
		if len(groupRelationInfos) == 0 {
			log.Infof("没有需要同步的分组关系数据")
			return
		}
		// 同步数据到远端服务
		_, err = m.syncGroupRelationInfosToRemote(groupRelationInfos)
		if err != nil {
			log.Errorf("同步分组关系数据到远端失败: %v", err)
			return
		}
		// 更新本地数据的同步状态
		var list []*dto.UpdateGroupRelationSyncStatusRequest
		for _, groupRelationInfo := range groupRelationInfos {
			info := &dto.UpdateGroupRelationSyncStatusRequest{Gid: groupRelationInfo.Gid, Member: groupRelationInfo.Member, OrgName: groupRelationInfo.OrgName, Version: groupRelationInfo.Version}
			list = append(list, info)
		}
		_, err = repository.Group.UpdateGroupRelationSyncStatus(context.Background(), list, 1)
		if err != nil {
			log.Errorf("更新分组关系同步状态失败: %v", err)
			return
		}
	}
	log.Infof("[group relation] 处理分组关系数据同步完成")
}

func (m *Monitor) syncGroupRelationInfosToRemote(groupRelationInfos []*entity.GroupRelation) (interface{}, error) {
	infos := make([]*infoGrpc.DeviceGroupRelationInfo, 0)
	for _, groupRelationInfo := range groupRelationInfos {
		info := &infoGrpc.DeviceGroupRelationInfo{
			Gid:        groupRelationInfo.Gid,
			OrgName:    groupRelationInfo.OrgName,
			Member:     groupRelationInfo.Member,
			CreateTime: groupRelationInfo.CreateTime,
			Status:     groupRelationInfo.Status,
			CreateBy:   groupRelationInfo.CreateBy,
			DeleteBy:   groupRelationInfo.DeleteBy,
			DeleteTime: groupRelationInfo.DeleteTime,
			Version:    groupRelationInfo.Version,
		}
		infos = append(infos, info)
	}
	if len(infos) == 0 {
		return nil, nil
	}
	res, err := client.InfoGrpcClient.ReportDeviceGroupRelationInfos(context.Background(), &infoGrpc.DeviceGroupRelationInfosRequest{
		Infos: infos,
	})
	if err != nil {
		log.Errorf("同步分组数据关系到远端失败: %v", err)
		return nil, err
	}
	if res.Count == int32(len(infos)) {
		log.Infof("同步分组数据关系到远端成功: %v", res.Count)
	} else {
		log.Errorf("同步分组数据关系到远端失败, total: %d, succeed: %d", len(infos), res.Count)
	}
	return res, nil
}
