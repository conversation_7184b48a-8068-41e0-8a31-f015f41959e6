package monitor

import (
	"context"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"time"

	"rm.git/client_api/rm_common_libs.git/v2/interfaces"
	"rm.git/client_api/rm_common_libs.git/v2/library/log"
)

// RedisLock Redis分布式锁
type RedisLock struct {
	client     interfaces.RedisClient
	key        string
	value      string
	expiration time.Duration
	isMaster   bool
	stopCh     chan struct{}
}

// NewRedisLock 创建Redis分布式锁
func NewRedisLock(client interfaces.RedisClient, key string, expiration time.Duration) *RedisLock {
	return &RedisLock{
		client:     client,
		key:        key,
		value:      fmt.Sprintf("%d", time.Now().UnixNano()),
		expiration: expiration,
		stopCh:     make(chan struct{}),
	}
}

// Lock 获取锁
func (r *RedisLock) Lock(ctx context.Context) (bool, error) {
	// 使用SET NX命令尝试获取锁
	result, err := r.client.SetNX(ctx, r.key, r.value, r.expiration).Result()
	if err != nil {
		log.Errorf("获取锁失败: %v", err)
		return false, fmt.Errorf("获取锁失败: %v", err)
	}
	return result, nil
}

// Unlock 释放锁
func (r *RedisLock) Unlock(ctx context.Context) error {
	// 先获取锁的值
	val, err := r.client.Get(ctx, r.key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return fmt.Errorf("锁不存在")
		}
		log.Errorf("获取锁值失败: %v", err)
		return err
	}

	// 检查是否是当前节点持有的锁
	if val != r.value {
		return fmt.Errorf("锁已被其他客户端持有")
	}

	// 删除锁
	result, err := r.client.Del(ctx, r.key).Result()
	if err != nil {
		log.Errorf("删除锁失败: %v", err)
		return fmt.Errorf("删除锁失败: %v", err)
	}
	if result == 0 {
		return fmt.Errorf("锁删除失败")
	}

	log.Infof("成功释放锁: key=%s", r.key)
	return nil
}

// ExtendLock 延长锁的过期时间
func (r *RedisLock) ExtendLock(ctx context.Context) error {
	// 先获取锁的值
	val, err := r.client.Get(ctx, r.key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return fmt.Errorf("锁不存在")
		}
		log.Errorf("获取锁值失败: %v", err)
		return err
	}

	// 检查是否是当前节点持有的锁
	if val != r.value {
		return fmt.Errorf("锁已被其他客户端持有")
	}

	// 直接使用EXPIRE命令延长过期时间
	success, err := r.client.Expire(ctx, r.key, r.expiration).Result()
	if err != nil {
		log.Errorf("延长锁过期时间失败: %v", err)
		return err
	}
	if !success {
		return fmt.Errorf("延长锁过期时间失败")
	}
	return nil
}

// StartMaster 启动主节点模式
func (r *RedisLock) StartMaster(ctx context.Context) error {
	// 尝试获取锁
	acquired, err := r.Lock(ctx)
	if err != nil {
		return err
	}
	if !acquired {
		return fmt.Errorf("无法获取锁，当前已有主节点")
	}

	r.isMaster = true
	log.Infof("当前节点成为主节点")

	// 启动看门狗
	go r.runWatchdog(ctx)

	return nil
}

// StartSlave 启动从节点模式
func (r *RedisLock) StartSlave(ctx context.Context) error {
	r.isMaster = false

	// 启动检查线程
	go r.runSlaveCheck(ctx)

	return nil
}

// runWatchdog 运行看门狗
func (r *RedisLock) runWatchdog(ctx context.Context) {
	ticker := time.NewTicker(r.expiration / 3)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := r.ExtendLock(ctx); err != nil {
				log.Errorf("看门狗续期失败: %v", err)
				r.isMaster = false
				// 启动自愈检查
				go r.startSelfHealing(ctx)
				return
			}
		case <-r.stopCh:
			return
		case <-ctx.Done():
			return
		}
	}
}

// startSelfHealing 启动自愈机制
func (r *RedisLock) startSelfHealing(ctx context.Context) {
	ticker := time.NewTicker(time.Second * 5) // 5秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 尝试重新获取锁
			acquired, err := r.Lock(ctx)
			if err != nil {
				log.Errorf("自愈重新获取锁失败: %v", err)
				continue
			}
			if acquired {
				r.isMaster = true
				log.Infof("自愈成功，重新成为主节点")
				go r.runWatchdog(ctx)
				return
			}
		case <-r.stopCh:
			return
		case <-ctx.Done():
			return
		}
	}
}

// runSlaveCheck 运行从节点检查
func (r *RedisLock) runSlaveCheck(ctx context.Context) {
	ticker := time.NewTicker(r.expiration / 2)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// 检查锁是否存在
			exists, err := r.client.Exists(ctx, r.key).Result()
			if err != nil {
				log.Errorf("检查锁状态失败: %v", err)
				continue
			}

			// 如果锁不存在，尝试获取锁
			if exists == 0 {
				acquired, err := r.Lock(ctx)
				if err != nil {
					log.Errorf("尝试获取锁失败: %v", err)
					continue
				}
				if acquired {
					r.isMaster = true
					// 启动看门狗
					go r.runWatchdog(ctx)
					return
				}
			}
		case <-r.stopCh:
			return
		case <-ctx.Done():
			return
		}
	}
}

// Stop 停止锁的监控
func (r *RedisLock) Stop() {
	close(r.stopCh)
	if r.isMaster {
		// 如果是主节点，释放锁
		ctx := context.Background()
		if err := r.Unlock(ctx); err != nil {
			log.Errorf("停止时释放锁失败: %v", err)
		}
	}
}

// IsMaster 检查当前节点是否为主节点
func (r *RedisLock) IsMaster() bool {
	return r.isMaster
}
